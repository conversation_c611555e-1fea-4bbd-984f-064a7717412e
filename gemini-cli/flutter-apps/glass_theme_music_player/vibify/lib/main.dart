import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'screens/main_navigation_screen.dart';
import 'services/audio_service.dart';
import 'services/favorites_service.dart';
import 'services/youtube_service.dart';
import 'services/download_service.dart';
import 'utils/app_theme.dart';
import 'utils/sample_data.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(AppTheme.lightSystemUI);

  runApp(const VibifyApp());
}

class VibifyApp extends StatelessWidget {
  const VibifyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Vibify - Glass Music Player',
      theme: AppTheme.darkTheme,
      debugShowCheckedModeBanner: false,
      home: const VibifyHomePage(),
    );
  }
}

class VibifyHomePage extends StatefulWidget {
  const VibifyHomePage({super.key});

  @override
  State<VibifyHomePage> createState() => _VibifyHomePageState();
}

class _VibifyHomePageState extends State<VibifyHomePage> {
  late AudioService _audioService;
  late FavoritesService _favoritesService;
  late YouTubeService _youtubeService;
  late DownloadService _downloadService;

  @override
  void initState() {
    super.initState();
    _audioService = AudioService();
    _favoritesService = FavoritesService();
    _youtubeService = YouTubeService();
    _downloadService = DownloadService();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    await _audioService.initialize();
    await _favoritesService.initialize();
    await _downloadService.initialize();

    // Load sample playlist
    await _audioService.loadPlaylist(SampleData.samplePlaylist);
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _audioService),
        ChangeNotifierProvider.value(value: _favoritesService),
        ChangeNotifierProvider.value(value: _youtubeService),
        ChangeNotifierProvider.value(value: _downloadService),
      ],
      child: const MainNavigationScreen(),
    );
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
