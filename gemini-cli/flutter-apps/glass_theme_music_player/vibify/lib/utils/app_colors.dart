import 'package:flutter/material.dart';

class AppColors {
  // Primary background colors
  static const Color primaryBackground = Color(0xFF0A0A0F);
  static const Color secondaryBackground = Color(0xFF1A1A2E);
  static const Color cardBackground = Color(0xFF16213E);
  
  // Glass effect colors
  static const Color glassBackground = Color(0x1AFFFFFF);
  static const Color glassBorder = Color(0x33FFFFFF);
  static const Color glassHighlight = Color(0x0DFFFFFF);
  
  // Gradient colors
  static const List<Color> primaryGradient = [
    Color(0xFF6A5ACD),  // Slate Blue
    Color(0xFF9370DB),  // Medium Purple
    Color(0xFFBA55D3),  // Medium Orchid
  ];
  
  static const List<Color> secondaryGradient = [
    Color(0xFF4B0082),  // Indigo
    Color(0xFF8A2BE2),  // Blue Violet
    Color(0xFFDA70D6),  // Orchid
  ];
  
  static const List<Color> accentGradient = [
    Color(0xFFFF6B6B),  // Light Red
    Color(0xFF4ECDC4),  // Turquoise
    Color(0xFF45B7D1),  // Sky Blue
    Color(0xFF96CEB4),  // Light Green
    Color(0xFFFECA57),  // Light Orange
    Color(0xFFFF9FF3),  // Light Pink
  ];
  
  // Waveform colors
  static const List<Color> waveformGradient = [
    Color(0xFFFF6B6B),  // Red
    Color(0xFFFFE66D),  // Yellow
    Color(0xFF4ECDC4),  // Turquoise
    Color(0xFF45B7D1),  // Blue
    Color(0xFFB8860B),  // Dark Golden Rod
    Color(0xFFFF69B4),  // Hot Pink
  ];
  
  // Text colors
  static const Color primaryText = Color(0xFFFFFFFF);
  static const Color secondaryText = Color(0xFFB0B0B0);
  static const Color tertiaryText = Color(0xFF808080);
  static const Color accentText = Color(0xFF9370DB);
  
  // Icon colors
  static const Color primaryIcon = Color(0xFFFFFFFF);
  static const Color secondaryIcon = Color(0xFFB0B0B0);
  static const Color accentIcon = Color(0xFF9370DB);
  
  // Progress and slider colors
  static const Color progressBackground = Color(0xFF2A2A3E);
  static const Color progressForeground = Color(0xFF9370DB);
  static const Color sliderThumb = Color(0xFFFFFFFF);
  
  // Button colors
  static const Color buttonBackground = Color(0x33FFFFFF);
  static const Color buttonBorder = Color(0x66FFFFFF);
  static const Color buttonPressed = Color(0x4DFFFFFF);
  
  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Shimmer colors
  static const Color shimmerBase = Color(0xFF2A2A3E);
  static const Color shimmerHighlight = Color(0xFF3A3A4E);
  
  // Shadow colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  
  // Gradient definitions
  static const LinearGradient primaryLinearGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: primaryGradient,
  );
  
  static const LinearGradient secondaryLinearGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: secondaryGradient,
  );
  
  static const LinearGradient waveformLinearGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: waveformGradient,
  );
  
  static const RadialGradient primaryRadialGradient = RadialGradient(
    center: Alignment.center,
    radius: 1.0,
    colors: primaryGradient,
  );
  
  // Glass morphism box decoration
  static BoxDecoration get glassDecoration => BoxDecoration(
    color: glassBackground,
    borderRadius: BorderRadius.circular(20),
    border: Border.all(
      color: glassBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: shadowMedium,
        blurRadius: 20,
        offset: const Offset(0, 8),
      ),
    ],
  );
  
  // Card glass decoration
  static BoxDecoration get cardGlassDecoration => BoxDecoration(
    color: glassBackground,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: glassBorder,
      width: 0.5,
    ),
    boxShadow: [
      BoxShadow(
        color: shadowLight,
        blurRadius: 10,
        offset: const Offset(0, 4),
      ),
    ],
  );
  
  // Button glass decoration
  static BoxDecoration get buttonGlassDecoration => BoxDecoration(
    color: buttonBackground,
    borderRadius: BorderRadius.circular(12),
    border: Border.all(
      color: buttonBorder,
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: shadowLight,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );
  
  // Progress ring decoration
  static BoxDecoration get progressRingDecoration => BoxDecoration(
    shape: BoxShape.circle,
    gradient: primaryLinearGradient,
    boxShadow: [
      BoxShadow(
        color: primaryGradient.first.withOpacity(0.3),
        blurRadius: 20,
        offset: const Offset(0, 0),
      ),
    ],
  );
}
