import '../models/song.dart';
import '../models/playlist.dart';

class SampleData {
  static List<Song> get sampleSongs => [
    Song(
      id: '1',
      title: 'Hello',
      artist: '<PERSON>',
      album: '25',
      albumArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
      duration: const Duration(minutes: 4, seconds: 55),
      genres: ['Pop', 'Soul'],
    ),
    Song(
      id: '2',
      title: 'Dog in the car',
      artist: 'THMB',
      album: 'Single',
      albumArt: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
      duration: const Duration(minutes: 5, seconds: 15),
      genres: ['Electronic', 'Ambient'],
    ),
    Song(
      id: '3',
      title: 'Mirrors',
      artist: '<PERSON>',
      album: 'The 20/20 Experience',
      albumArt: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
      duration: const Duration(minutes: 8, seconds: 5),
      genres: ['Pop', 'R&B'],
    ),
    Song(
      id: '4',
      title: 'Dusk Till Dawn',
      artist: 'Zayn ft. Sia',
      album: 'Icarus Falls',
      albumArt: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
      duration: const Duration(minutes: 6, seconds: 26),
      genres: ['Pop', 'Electronic'],
    ),
    Song(
      id: '5',
      title: 'Burn',
      artist: 'Ellie Goulding',
      album: 'Halcyon Days',
      albumArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3',
      duration: const Duration(minutes: 4, seconds: 52),
      genres: ['Pop', 'Electronic'],
    ),
    Song(
      id: '6',
      title: 'Bring Me To Life',
      artist: 'Evanescence',
      album: 'Fallen',
      albumArt: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3',
      duration: const Duration(minutes: 6, seconds: 3),
      genres: ['Rock', 'Alternative'],
    ),
    Song(
      id: '7',
      title: 'Blinding Lights',
      artist: 'The Weeknd',
      album: 'After Hours',
      albumArt: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3',
      duration: const Duration(minutes: 3, seconds: 20),
      genres: ['Pop', 'Synthwave'],
    ),
    Song(
      id: '8',
      title: 'Watermelon Sugar',
      artist: 'Harry Styles',
      album: 'Fine Line',
      albumArt: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
      duration: const Duration(minutes: 2, seconds: 54),
      genres: ['Pop', 'Rock'],
    ),
    Song(
      id: '9',
      title: 'Levitating',
      artist: 'Dua Lipa',
      album: 'Future Nostalgia',
      albumArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
      duration: const Duration(minutes: 3, seconds: 23),
      genres: ['Pop', 'Disco'],
    ),
    Song(
      id: '10',
      title: 'Good 4 U',
      artist: 'Olivia Rodrigo',
      album: 'SOUR',
      albumArt: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=400&fit=crop',
      audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3',
      duration: const Duration(minutes: 2, seconds: 58),
      genres: ['Pop', 'Rock'],
    ),
  ];

  static Playlist get samplePlaylist => Playlist(
    id: 'playlist_1',
    name: 'Chandelier',
    description: 'A collection of popular hits and favorites',
    coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
    songs: sampleSongs,
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    updatedAt: DateTime.now(),
  );

  static List<Playlist> get samplePlaylists => [
    samplePlaylist,
    Playlist(
      id: 'playlist_2',
      name: 'Chill Vibes',
      description: 'Relaxing songs for a peaceful mood',
      coverImage: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=400&fit=crop',
      songs: sampleSongs.take(5).toList(),
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Playlist(
      id: 'playlist_3',
      name: 'Workout Mix',
      description: 'High-energy songs to keep you motivated',
      coverImage: 'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=400&fit=crop',
      songs: sampleSongs.skip(3).take(4).toList(),
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
  ];

  // Alternative album art URLs for variety
  static List<String> get albumArtUrls => [
    'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1518609878373-06d740f60d8b?w=400&h=400&fit=crop',
    'https://images.unsplash.com/photo-1511671782779-c97d3d27a1d4?w=400&h=400&fit=crop',
  ];

  // Sample audio URLs (using free audio samples)
  static List<String> get audioUrls => [
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-5.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-6.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-7.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-8.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-9.mp3',
    'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-10.mp3',
  ];

  // Generate random song for testing
  static Song generateRandomSong(int index) {
    final titles = [
      'Midnight Dreams', 'Electric Pulse', 'Neon Lights', 'Crystal Waters',
      'Starlight Symphony', 'Digital Horizon', 'Velvet Skies', 'Golden Hour',
      'Infinite Loop', 'Cosmic Dance', 'Silent Echoes', 'Radiant Dawn'
    ];
    
    final artists = [
      'Luna Eclipse', 'Neon Collective', 'Crystal Method', 'Digital Dreams',
      'Starlight Orchestra', 'Horizon Band', 'Velvet Voice', 'Golden Era',
      'Loop Masters', 'Cosmic Crew', 'Echo Chamber', 'Dawn Chorus'
    ];
    
    final albums = [
      'Night Sessions', 'Electric Collection', 'Neon Anthology', 'Crystal Clear',
      'Stellar Sounds', 'Digital Age', 'Velvet Touch', 'Golden Memories',
      'Infinite Possibilities', 'Cosmic Journey', 'Silent Moments', 'New Beginnings'
    ];

    return Song(
      id: 'random_$index',
      title: titles[index % titles.length],
      artist: artists[index % artists.length],
      album: albums[index % albums.length],
      albumArt: albumArtUrls[index % albumArtUrls.length],
      audioUrl: audioUrls[index % audioUrls.length],
      duration: Duration(
        minutes: 2 + (index % 6),
        seconds: (index * 13) % 60,
      ),
      genres: ['Electronic', 'Ambient', 'Pop'][index % 3] == 'Electronic' 
        ? ['Electronic', 'Ambient']
        : ['Pop', 'Alternative'],
    );
  }
}
