import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/song.dart';

class FavoritesService extends ChangeNotifier {
  static final FavoritesService _instance = FavoritesService._internal();
  factory FavoritesService() => _instance;
  FavoritesService._internal();

  static const String _favoritesKey = 'favorite_songs';
  List<Song> _favoriteSongs = [];
  SharedPreferences? _prefs;

  List<Song> get favoriteSongs => List.unmodifiable(_favoriteSongs);

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    try {
      final favoritesJson = _prefs?.getStringList(_favoritesKey) ?? [];
      _favoriteSongs = favoritesJson
          .map((jsonString) => Song.fromJson(json.decode(jsonString)))
          .toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading favorites: $e');
      _favoriteSongs = [];
    }
  }

  Future<void> _saveFavorites() async {
    try {
      final favoritesJson = _favoriteSongs
          .map((song) => json.encode(song.toJson()))
          .toList();
      await _prefs?.setStringList(_favoritesKey, favoritesJson);
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }

  bool isFavorite(Song song) {
    return _favoriteSongs.any((favSong) => favSong.id == song.id);
  }

  Future<void> toggleFavorite(Song song) async {
    if (isFavorite(song)) {
      await removeFavorite(song);
    } else {
      await addFavorite(song);
    }
  }

  Future<void> addFavorite(Song song) async {
    if (!isFavorite(song)) {
      _favoriteSongs.add(song);
      await _saveFavorites();
      notifyListeners();
    }
  }

  Future<void> removeFavorite(Song song) async {
    _favoriteSongs.removeWhere((favSong) => favSong.id == song.id);
    await _saveFavorites();
    notifyListeners();
  }

  Future<void> clearAllFavorites() async {
    _favoriteSongs.clear();
    await _saveFavorites();
    notifyListeners();
  }

  List<Song> getFavoritesByGenre(String genre) {
    return _favoriteSongs
        .where((song) => song.genres.contains(genre))
        .toList();
  }

  List<Song> getFavoritesByArtist(String artist) {
    return _favoriteSongs
        .where((song) => song.artist.toLowerCase().contains(artist.toLowerCase()))
        .toList();
  }

  List<Song> searchFavorites(String query) {
    final lowerQuery = query.toLowerCase();
    return _favoriteSongs.where((song) {
      return song.title.toLowerCase().contains(lowerQuery) ||
          song.artist.toLowerCase().contains(lowerQuery) ||
          song.album.toLowerCase().contains(lowerQuery);
    }).toList();
  }
}
