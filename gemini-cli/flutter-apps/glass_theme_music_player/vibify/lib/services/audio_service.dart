import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import '../models/song.dart';
import '../models/playlist.dart';
import '../models/player_state.dart';

class AudioService extends ChangeNotifier {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  MusicPlayerState _playerState = const MusicPlayerState();
  List<Song> _originalQueue = [];
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  MusicPlayerState get playerState => _playerState;
  AudioPlayer get audioPlayer => _audioPlayer;

  Future<void> initialize() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());

      _setupPlayerListeners();
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(error: 'Failed to initialize audio service: $e'),
      );
    }
  }

  void _setupPlayerListeners() {
    // Position updates
    _positionSubscription = _audioPlayer.positionStream.listen((position) {
      _updatePlayerState(_playerState.copyWith(position: position));
    });

    // Duration updates
    _durationSubscription = _audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        _updatePlayerState(_playerState.copyWith(duration: duration));
      }
    });

    // Player state updates
    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      PlaybackState playbackState;
      switch (state.processingState) {
        case ProcessingState.idle:
          playbackState = PlaybackState.stopped;
          break;
        case ProcessingState.loading:
        case ProcessingState.buffering:
          playbackState = PlaybackState.buffering;
          break;
        case ProcessingState.ready:
          playbackState = state.playing
              ? PlaybackState.playing
              : PlaybackState.paused;
          break;
        case ProcessingState.completed:
          playbackState = PlaybackState.stopped;
          _handleSongCompleted();
          break;
      }

      _updatePlayerState(
        _playerState.copyWith(
          playbackState: playbackState,
          isLoading: state.processingState == ProcessingState.loading,
        ),
      );
    });
  }

  Future<void> loadPlaylist(Playlist playlist, {int startIndex = 0}) async {
    try {
      _updatePlayerState(
        _playerState.copyWith(isLoading: true, clearError: true),
      );

      final audioSources = playlist.songs
          .map((song) => AudioSource.uri(Uri.parse(song.audioUrl)))
          .toList();

      final concatenatingAudioSource = ConcatenatingAudioSource(
        children: audioSources,
      );

      await _audioPlayer.setAudioSource(
        concatenatingAudioSource,
        initialIndex: startIndex,
      );

      _originalQueue = List.from(playlist.songs);

      _updatePlayerState(
        _playerState.copyWith(
          currentPlaylist: playlist,
          queue: playlist.songs,
          currentIndex: startIndex,
          currentSong: playlist.songs[startIndex],
          isLoading: false,
        ),
      );
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(
          isLoading: false,
          error: 'Failed to load playlist: $e',
        ),
      );
    }
  }

  Future<void> loadSong(Song song) async {
    try {
      _updatePlayerState(
        _playerState.copyWith(isLoading: true, clearError: true),
      );

      await _audioPlayer.setUrl(song.audioUrl);

      _updatePlayerState(
        _playerState.copyWith(
          currentSong: song,
          queue: [song],
          currentIndex: 0,
          isLoading: false,
        ),
      );
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(
          isLoading: false,
          error: 'Failed to load song: $e',
        ),
      );
    }
  }

  Future<void> play() async {
    try {
      await _audioPlayer.play();
    } catch (e) {
      _updatePlayerState(_playerState.copyWith(error: 'Failed to play: $e'));
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _updatePlayerState(_playerState.copyWith(error: 'Failed to pause: $e'));
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      _updatePlayerState(_playerState.copyWith(error: 'Failed to stop: $e'));
    }
  }

  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      _updatePlayerState(_playerState.copyWith(error: 'Failed to seek: $e'));
    }
  }

  Future<void> next() async {
    if (!_playerState.canGoNext && _playerState.repeatMode != RepeatMode.all) {
      return;
    }

    try {
      int nextIndex;
      if (_playerState.currentIndex >= _playerState.queue.length - 1) {
        if (_playerState.repeatMode == RepeatMode.all) {
          nextIndex = 0;
        } else {
          return;
        }
      } else {
        nextIndex = _playerState.currentIndex + 1;
      }

      await _audioPlayer.seekToNext();
      _updatePlayerState(
        _playerState.copyWith(
          currentIndex: nextIndex,
          currentSong: _playerState.queue[nextIndex],
        ),
      );
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(error: 'Failed to go to next song: $e'),
      );
    }
  }

  Future<void> previous() async {
    if (!_playerState.canGoPrevious) {
      await seek(Duration.zero);
      return;
    }

    try {
      final previousIndex = _playerState.currentIndex - 1;
      await _audioPlayer.seekToPrevious();
      _updatePlayerState(
        _playerState.copyWith(
          currentIndex: previousIndex,
          currentSong: _playerState.queue[previousIndex],
        ),
      );
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(error: 'Failed to go to previous song: $e'),
      );
    }
  }

  void toggleShuffle() {
    final newShuffleState = !_playerState.isShuffleEnabled;
    List<Song> newQueue;

    if (newShuffleState) {
      // Enable shuffle
      newQueue = List.from(_originalQueue);
      final currentSong = _playerState.currentSong;
      newQueue.remove(currentSong);
      newQueue.shuffle(Random());
      if (currentSong != null) {
        newQueue.insert(0, currentSong);
      }
    } else {
      // Disable shuffle
      newQueue = List.from(_originalQueue);
    }

    _updatePlayerState(
      _playerState.copyWith(
        isShuffleEnabled: newShuffleState,
        queue: newQueue,
        currentIndex: 0,
      ),
    );
  }

  void toggleRepeat() {
    RepeatMode newRepeatMode;
    switch (_playerState.repeatMode) {
      case RepeatMode.off:
        newRepeatMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        newRepeatMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        newRepeatMode = RepeatMode.off;
        break;
    }

    _updatePlayerState(_playerState.copyWith(repeatMode: newRepeatMode));
  }

  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
      _updatePlayerState(_playerState.copyWith(volume: volume));
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(error: 'Failed to set volume: $e'),
      );
    }
  }

  Future<void> playFromQueue(int index) async {
    if (index < 0 || index >= _playerState.queue.length) return;

    try {
      await _audioPlayer.seek(Duration.zero, index: index);
      _updatePlayerState(
        _playerState.copyWith(
          currentIndex: index,
          currentSong: _playerState.queue[index],
        ),
      );
      await play();
    } catch (e) {
      _updatePlayerState(
        _playerState.copyWith(error: 'Failed to play from queue: $e'),
      );
    }
  }

  void _handleSongCompleted() {
    if (_playerState.repeatMode == RepeatMode.one) {
      _audioPlayer.seek(Duration.zero);
      _audioPlayer.play();
    } else {
      next();
    }
  }

  void _updatePlayerState(MusicPlayerState newState) {
    _playerState = newState;
    notifyListeners();
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playerStateSubscription?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }
}
