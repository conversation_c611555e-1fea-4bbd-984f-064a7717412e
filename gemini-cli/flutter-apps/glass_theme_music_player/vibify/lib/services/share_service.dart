import 'package:share_plus/share_plus.dart';
import '../models/song.dart';
import '../models/playlist.dart';

class ShareService {
  static Future<void> shareSong(Song song) async {
    final text = '''
🎵 Check out this song!

🎤 ${song.title}
👨‍🎤 ${song.artist}
💿 ${song.album}
⏱️ ${song.formattedDuration}

Shared from Vibify Music Player 🎶
''';

    await Share.share(
      text,
      subject: 'Check out "${song.title}" by ${song.artist}',
    );
  }

  static Future<void> sharePlaylist(Playlist playlist) async {
    final songList = playlist.songs
        .take(5) // Show first 5 songs
        .map((song) => '• ${song.title} - ${song.artist}')
        .join('\n');

    final moreText = playlist.songs.length > 5 
        ? '\n... and ${playlist.songs.length - 5} more songs'
        : '';

    final text = '''
🎵 Check out this playlist!

📂 ${playlist.name}
📝 ${playlist.description}
🎵 ${playlist.songCount} songs • ${playlist.formattedTotalDuration}

Songs:
$songList$moreText

Shared from Vibify Music Player 🎶
''';

    await Share.share(
      text,
      subject: 'Check out the "${playlist.name}" playlist',
    );
  }

  static Future<void> shareCurrentlyPlaying(Song song, Duration currentPosition) async {
    final positionText = _formatDuration(currentPosition);
    final text = '''
🎵 Currently listening to:

🎤 ${song.title}
👨‍🎤 ${song.artist}
💿 ${song.album}
⏱️ $positionText / ${song.formattedDuration}

Join me on Vibify Music Player! 🎶
''';

    await Share.share(
      text,
      subject: 'Currently listening to "${song.title}"',
    );
  }

  static Future<void> shareApp() async {
    const text = '''
🎵 Discover amazing music with Vibify!

✨ Beautiful glass-themed music player
🎶 Stream and download your favorite songs
📱 Elegant and intuitive interface
🎧 High-quality audio experience

Download Vibify Music Player now! 🎶
''';

    await Share.share(
      text,
      subject: 'Check out Vibify Music Player!',
    );
  }

  static Future<void> shareWithImage(String text, String imagePath) async {
    await Share.shareXFiles(
      [XFile(imagePath)],
      text: text,
    );
  }

  static String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
