import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import '../models/song.dart';
import '../models/playlist.dart' as app_models;

class YouTubeService extends ChangeNotifier {
  static final YouTubeService _instance = YouTubeService._internal();
  factory YouTubeService() => _instance;
  YouTubeService._internal();

  final YoutubeExplode _youtubeExplode = YoutubeExplode();
  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<List<Song>> searchSongs(String query, {int maxResults = 20}) async {
    try {
      _setLoading(true);
      _clearError();

      final searchResults = await _youtubeExplode.search.search(query);
      final songs = <Song>[];

      for (int i = 0; i < maxResults && i < searchResults.length; i++) {
        final video = searchResults[i];

        try {
          final song = await _videoToSong(video);
          songs.add(song);
        } catch (e) {
          debugPrint('Error converting video to song: $e');
          continue;
        }
      }

      _setLoading(false);
      return songs;
    } catch (e) {
      _setError('Failed to search songs: $e');
      _setLoading(false);
      return [];
    }
  }

  Future<Song> _videoToSong(Video video) async {
    // Get high quality thumbnail
    String thumbnailUrl = '';
    try {
      // Try to get maxresdefault thumbnail first
      thumbnailUrl =
          'https://img.youtube.com/vi/${video.id.value}/maxresdefault.jpg';
    } catch (e) {
      // Fallback to default thumbnail
      thumbnailUrl = 'https://img.youtube.com/vi/${video.id.value}/default.jpg';
    }

    // Extract artist and title from video title
    final titleParts = _extractArtistAndTitle(video.title);

    return Song(
      id: video.id.value,
      title: titleParts['title'] ?? video.title,
      artist: titleParts['artist'] ?? video.author,
      album: 'YouTube',
      albumArt: thumbnailUrl,
      audioUrl: video.id.value, // We'll use the video ID as URL identifier
      duration: video.duration ?? Duration.zero,
      genres: ['YouTube'],
    );
  }

  Map<String, String> _extractArtistAndTitle(String videoTitle) {
    // Common patterns for YouTube music videos
    final patterns = [
      RegExp(r'^(.+?)\s*-\s*(.+)$'), // Artist - Title
      RegExp(r'^(.+?)\s*–\s*(.+)$'), // Artist – Title (em dash)
      RegExp(r'^(.+?)\s*\|\s*(.+)$'), // Artist | Title
      RegExp(r'^(.+?)\s*by\s*(.+)$'), // Title by Artist
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(videoTitle);
      if (match != null) {
        if (videoTitle.contains(' by ')) {
          return {
            'title': match.group(1)?.trim() ?? videoTitle,
            'artist': match.group(2)?.trim() ?? 'Unknown Artist',
          };
        } else {
          return {
            'artist': match.group(1)?.trim() ?? 'Unknown Artist',
            'title': match.group(2)?.trim() ?? videoTitle,
          };
        }
      }
    }

    // If no pattern matches, return the full title
    return {'title': videoTitle, 'artist': 'Unknown Artist'};
  }

  Future<String> getStreamUrl(String videoId) async {
    try {
      final manifest = await _youtubeExplode.videos.streamsClient.getManifest(
        videoId,
      );

      // Get the best audio-only stream
      final audioStreams = manifest.audioOnly;
      if (audioStreams.isNotEmpty) {
        // Prefer higher bitrate streams
        final bestStream = audioStreams.reduce(
          (a, b) => a.bitrate.bitsPerSecond > b.bitrate.bitsPerSecond ? a : b,
        );
        return bestStream.url.toString();
      }

      // Fallback to muxed streams if no audio-only available
      final muxedStreams = manifest.muxed;
      if (muxedStreams.isNotEmpty) {
        final bestStream = muxedStreams.reduce(
          (a, b) => a.bitrate.bitsPerSecond > b.bitrate.bitsPerSecond ? a : b,
        );
        return bestStream.url.toString();
      }

      throw Exception('No suitable stream found');
    } catch (e) {
      throw Exception('Failed to get stream URL: $e');
    }
  }

  Future<List<Song>> getTrendingSongs({int maxResults = 20}) async {
    try {
      _setLoading(true);
      _clearError();

      // Search for trending music
      final queries = [
        'trending music 2024',
        'popular songs 2024',
        'top hits 2024',
        'viral songs',
        'chart toppers',
      ];

      final allSongs = <Song>[];

      for (final query in queries) {
        final songs = await searchSongs(query, maxResults: 5);
        allSongs.addAll(songs);
        if (allSongs.length >= maxResults) break;
      }

      _setLoading(false);
      return allSongs.take(maxResults).toList();
    } catch (e) {
      _setError('Failed to get trending songs: $e');
      _setLoading(false);
      return [];
    }
  }

  Future<List<Song>> getRecommendations(
    String basedOn, {
    int maxResults = 10,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final searchQuery = 'songs like $basedOn';
      final songs = await searchSongs(searchQuery, maxResults: maxResults);

      _setLoading(false);
      return songs;
    } catch (e) {
      _setError('Failed to get recommendations: $e');
      _setLoading(false);
      return [];
    }
  }

  Future<app_models.Playlist> createPlaylistFromSearch(
    String query, {
    int maxSongs = 50,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final songs = await searchSongs(query, maxResults: maxSongs);

      final playlist = app_models.Playlist(
        id: 'youtube_${DateTime.now().millisecondsSinceEpoch}',
        name: 'YouTube: $query',
        description: 'Auto-generated playlist from YouTube search',
        coverImage: songs.isNotEmpty ? songs.first.albumArt : '',
        songs: songs,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _setLoading(false);
      return playlist;
    } catch (e) {
      _setError('Failed to create playlist: $e');
      _setLoading(false);
      rethrow;
    }
  }

  Future<List<Song>> getArtistTopSongs(
    String artistName, {
    int maxResults = 20,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final searchQuery = '$artistName top songs';
      final songs = await searchSongs(searchQuery, maxResults: maxResults);

      _setLoading(false);
      return songs;
    } catch (e) {
      _setError('Failed to get artist songs: $e');
      _setLoading(false);
      return [];
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _youtubeExplode.close();
    super.dispose();
  }
}
