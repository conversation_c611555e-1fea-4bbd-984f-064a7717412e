import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/song.dart';
import 'youtube_service.dart';

enum DownloadStatus {
  pending,
  downloading,
  completed,
  failed,
  cancelled,
}

class DownloadItem {
  final String id;
  final Song song;
  final String downloadPath;
  DownloadStatus status;
  double progress;
  String? error;
  CancelToken? cancelToken;

  DownloadItem({
    required this.id,
    required this.song,
    required this.downloadPath,
    this.status = DownloadStatus.pending,
    this.progress = 0.0,
    this.error,
    this.cancelToken,
  });
}

class DownloadService extends ChangeNotifier {
  static final DownloadService _instance = DownloadService._internal();
  factory DownloadService() => _instance;
  DownloadService._internal();

  final Dio _dio = Dio();
  final YouTubeService _youtubeService = YouTubeService();
  final Map<String, DownloadItem> _downloads = {};
  final List<Song> _downloadedSongs = [];

  Map<String, DownloadItem> get downloads => Map.unmodifiable(_downloads);
  List<Song> get downloadedSongs => List.unmodifiable(_downloadedSongs);

  Future<void> initialize() async {
    await _loadDownloadedSongs();
  }

  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission for app documents
  }

  Future<String> _getDownloadDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final downloadDir = Directory('${directory.path}/downloads');
    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }
    return downloadDir.path;
  }

  Future<void> downloadSong(Song song) async {
    if (_downloads.containsKey(song.id)) {
      return; // Already downloading or downloaded
    }

    if (!await _requestPermissions()) {
      throw Exception('Storage permission denied');
    }

    try {
      final downloadDir = await _getDownloadDirectory();
      final fileName = '${song.title.replaceAll(RegExp(r'[^\w\s-]'), '')}.mp3';
      final downloadPath = '$downloadDir/$fileName';

      final cancelToken = CancelToken();
      final downloadItem = DownloadItem(
        id: song.id,
        song: song,
        downloadPath: downloadPath,
        status: DownloadStatus.pending,
        cancelToken: cancelToken,
      );

      _downloads[song.id] = downloadItem;
      notifyListeners();

      // Get the actual stream URL from YouTube
      String streamUrl;
      if (!song.audioUrl.startsWith('http') && song.audioUrl.length == 11) {
        streamUrl = await _youtubeService.getStreamUrl(song.audioUrl);
      } else {
        streamUrl = song.audioUrl;
      }

      downloadItem.status = DownloadStatus.downloading;
      notifyListeners();

      await _dio.download(
        streamUrl,
        downloadPath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            downloadItem.progress = received / total;
            notifyListeners();
          }
        },
      );

      downloadItem.status = DownloadStatus.completed;
      downloadItem.progress = 1.0;
      
      // Create a new song object with local file path
      final downloadedSong = Song(
        id: song.id,
        title: song.title,
        artist: song.artist,
        album: song.album,
        albumArt: song.albumArt,
        audioUrl: downloadPath, // Use local file path
        duration: song.duration,
        genres: [...song.genres, 'Downloaded'],
      );

      _downloadedSongs.add(downloadedSong);
      await _saveDownloadedSongs();
      notifyListeners();

    } catch (e) {
      if (_downloads.containsKey(song.id)) {
        _downloads[song.id]!.status = DownloadStatus.failed;
        _downloads[song.id]!.error = e.toString();
        notifyListeners();
      }
      rethrow;
    }
  }

  void cancelDownload(String songId) {
    final downloadItem = _downloads[songId];
    if (downloadItem != null && downloadItem.status == DownloadStatus.downloading) {
      downloadItem.cancelToken?.cancel();
      downloadItem.status = DownloadStatus.cancelled;
      _downloads.remove(songId);
      notifyListeners();
    }
  }

  void removeDownload(String songId) {
    final downloadItem = _downloads[songId];
    if (downloadItem != null) {
      // Delete the file if it exists
      final file = File(downloadItem.downloadPath);
      if (file.existsSync()) {
        file.deleteSync();
      }
      
      _downloads.remove(songId);
      _downloadedSongs.removeWhere((song) => song.id == songId);
      _saveDownloadedSongs();
      notifyListeners();
    }
  }

  bool isDownloaded(String songId) {
    return _downloadedSongs.any((song) => song.id == songId);
  }

  bool isDownloading(String songId) {
    final downloadItem = _downloads[songId];
    return downloadItem?.status == DownloadStatus.downloading;
  }

  double getDownloadProgress(String songId) {
    final downloadItem = _downloads[songId];
    return downloadItem?.progress ?? 0.0;
  }

  Song? getDownloadedSong(String songId) {
    try {
      return _downloadedSongs.firstWhere((song) => song.id == songId);
    } catch (e) {
      return null;
    }
  }

  Future<void> _loadDownloadedSongs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/downloaded_songs.json');
      
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        // TODO: Implement JSON parsing for downloaded songs
        // For now, we'll just initialize an empty list
        _downloadedSongs.clear();
      }
    } catch (e) {
      debugPrint('Error loading downloaded songs: $e');
    }
  }

  Future<void> _saveDownloadedSongs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/downloaded_songs.json');
      
      // TODO: Implement JSON serialization for downloaded songs
      // For now, we'll just create an empty file
      await file.writeAsString('[]');
    } catch (e) {
      debugPrint('Error saving downloaded songs: $e');
    }
  }

  Future<void> clearAllDownloads() async {
    for (final downloadItem in _downloads.values) {
      final file = File(downloadItem.downloadPath);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
    
    _downloads.clear();
    _downloadedSongs.clear();
    await _saveDownloadedSongs();
    notifyListeners();
  }

  Future<double> getDownloadedSize() async {
    double totalSize = 0;
    for (final song in _downloadedSongs) {
      final file = File(song.audioUrl);
      if (file.existsSync()) {
        totalSize += await file.length();
      }
    }
    return totalSize;
  }

  String formatFileSize(double bytes) {
    if (bytes < 1024) return '${bytes.toStringAsFixed(0)} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  void dispose() {
    _dio.close();
    super.dispose();
  }
}
