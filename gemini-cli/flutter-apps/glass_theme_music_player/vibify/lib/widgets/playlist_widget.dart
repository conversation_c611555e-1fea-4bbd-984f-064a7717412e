import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../utils/app_colors.dart';
import '../models/song.dart';
import '../models/playlist.dart';
import '../models/player_state.dart';

class PlaylistWidget extends StatelessWidget {
  final Playlist? playlist;
  final MusicPlayerState playerState;
  final Function(Song, int)? onSongTap;
  final VoidCallback? onShuffleAll;
  final double width;

  const PlaylistWidget({
    super.key,
    this.playlist,
    required this.playerState,
    this.onSongTap,
    this.onShuffleAll,
    this.width = 350,
  });

  @override
  Widget build(BuildContext context) {
    if (playlist == null || playlist!.songs.isEmpty) {
      return _buildEmptyPlaylist();
    }

    return Container(
      width: width,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPlaylistHeader(),
          const SizedBox(height: 16),
          _buildCurrentlyPlaying(),
          const SizedBox(height: 16),
          Expanded(child: _buildSongList()),
        ],
      ),
    );
  }

  Widget _buildPlaylistHeader() {
    return GlassmorphicContainer(
      width: double.infinity,
      height: 80,
      borderRadius: 16,
      blur: 20,
      alignment: Alignment.center,
      border: 1,
      linearGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.glassBackground,
          AppColors.glassBackground.withOpacity(0.1),
        ],
      ),
      borderGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [AppColors.glassBorder, AppColors.glassBorder.withOpacity(0.5)],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    playlist?.name ?? 'Playlist',
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${playlist?.songCount ?? 0} songs • ${playlist?.formattedTotalDuration ?? '0m'}',
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: onShuffleAll,
              child: Container(
                width: 40,
                height: 40,
                decoration: AppColors.buttonGlassDecoration,
                child: const Icon(
                  Icons.shuffle,
                  color: AppColors.primaryIcon,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentlyPlaying() {
    if (playerState.currentSong == null) {
      return const SizedBox.shrink();
    }

    return GlassmorphicContainer(
      width: double.infinity,
      height: 70,
      borderRadius: 12,
      blur: 20,
      alignment: Alignment.center,
      border: 1,
      linearGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.accentIcon.withOpacity(0.1),
          AppColors.accentIcon.withOpacity(0.05),
        ],
      ),
      borderGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.accentIcon.withOpacity(0.3),
          AppColors.accentIcon.withOpacity(0.1),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Play/Pause indicator
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: playerState.isPlaying
                    ? AppColors.success
                    : AppColors.secondaryText,
              ),
            ),
            const SizedBox(width: 12),

            // Song info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    playerState.currentSong!.title,
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    playerState.currentSong!.artist,
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Duration
            Text(
              playerState.currentSong!.formattedDuration,
              style: const TextStyle(
                color: AppColors.tertiaryText,
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSongList() {
    return ListView.builder(
      itemCount: playlist!.songs.length,
      itemBuilder: (context, index) {
        final song = playlist!.songs[index];
        final isCurrentSong = playerState.currentSong?.id == song.id;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildSongItem(song, index, isCurrentSong),
        );
      },
    );
  }

  Widget _buildSongItem(Song song, int index, bool isCurrentSong) {
    return GestureDetector(
      onTap: () => onSongTap?.call(song, index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isCurrentSong
              ? AppColors.accentIcon.withOpacity(0.1)
              : Colors.transparent,
          border: isCurrentSong
              ? Border.all(
                  color: AppColors.accentIcon.withOpacity(0.3),
                  width: 1,
                )
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Play indicator or track number
              SizedBox(
                width: 24,
                child: isCurrentSong
                    ? Icon(
                        playerState.isPlaying ? Icons.pause : Icons.play_arrow,
                        color: AppColors.accentIcon,
                        size: 16,
                      )
                    : Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: AppColors.tertiaryText,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ),
              const SizedBox(width: 12),

              // Song info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      song.title,
                      style: TextStyle(
                        color: isCurrentSong
                            ? AppColors.accentText
                            : AppColors.primaryText,
                        fontSize: 14,
                        fontWeight: isCurrentSong
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      song.artist,
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Duration
              Text(
                song.formattedDuration,
                style: const TextStyle(
                  color: AppColors.tertiaryText,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyPlaylist() {
    return Container(
      width: width,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.queue_music,
            size: 64,
            color: AppColors.tertiaryText.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'No songs in playlist',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add some music to get started',
            style: TextStyle(color: AppColors.tertiaryText, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
