import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../utils/app_colors.dart';
import '../models/song.dart';

class CircularAlbumArt extends StatefulWidget {
  final Song? song;
  final double progress;
  final bool isPlaying;
  final double size;
  final VoidCallback? onTap;

  const CircularAlbumArt({
    super.key,
    this.song,
    this.progress = 0.0,
    this.isPlaying = false,
    this.size = 280,
    this.onTap,
  });

  @override
  State<CircularAlbumArt> createState() => _CircularAlbumArtState();
}

class _CircularAlbumArtState extends State<CircularAlbumArt>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(CircularAlbumArt oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying && !oldWidget.isPlaying) {
      _rotationController.repeat();
    } else if (!widget.isPlaying && oldWidget.isPlaying) {
      _rotationController.stop();
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Outer glow effect
            if (widget.isPlaying)
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: widget.size + 20,
                      height: widget.size + 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primaryGradient.first.withOpacity(0.3),
                            AppColors.primaryGradient.last.withOpacity(0.1),
                            Colors.transparent,
                          ],
                          stops: const [0.0, 0.7, 1.0],
                        ),
                      ),
                    ),
                  );
                },
              ),
            
            // Progress ring
            CustomPaint(
              size: Size(widget.size, widget.size),
              painter: ProgressRingPainter(
                progress: widget.progress,
                strokeWidth: 4,
                colors: AppColors.primaryGradient,
              ),
            ),
            
            // Glass container for album art
            Container(
              width: widget.size - 20,
              height: widget.size - 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular((widget.size - 20) / 2),
                child: GlassmorphicContainer(
                  width: widget.size - 20,
                  height: widget.size - 20,
                  borderRadius: (widget.size - 20) / 2,
                  blur: 20,
                  alignment: Alignment.center,
                  border: 2,
                  linearGradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.glassBackground,
                      AppColors.glassBackground.withOpacity(0.1),
                    ],
                  ),
                  borderGradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.glassBorder,
                      AppColors.glassBorder.withOpacity(0.5),
                    ],
                  ),
                  child: AnimatedBuilder(
                    animation: _rotationController,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: widget.isPlaying ? _rotationController.value * 2 * math.pi : 0,
                        child: _buildAlbumArtContent(),
                      );
                    },
                  ),
                ),
              ),
            ),
            
            // Center dot
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primaryText,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowMedium,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
            
            // Inner center dot
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primaryBackground,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlbumArtContent() {
    if (widget.song?.albumArt != null && widget.song!.albumArt.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular((widget.size - 20) / 2),
        child: Image.network(
          widget.song!.albumArt,
          width: widget.size - 20,
          height: widget.size - 20,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultAlbumArt();
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return _buildLoadingAlbumArt();
          },
        ),
      );
    } else {
      return _buildDefaultAlbumArt();
    }
  }

  Widget _buildDefaultAlbumArt() {
    return Container(
      width: widget.size - 20,
      height: widget.size - 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: AppColors.secondaryLinearGradient,
      ),
      child: const Icon(
        Icons.music_note,
        size: 80,
        color: AppColors.primaryText,
      ),
    );
  }

  Widget _buildLoadingAlbumArt() {
    return Container(
      width: widget.size - 20,
      height: widget.size - 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.shimmerBase,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppColors.progressForeground,
          strokeWidth: 2,
        ),
      ),
    );
  }
}

class ProgressRingPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final List<Color> colors;

  ProgressRingPainter({
    required this.progress,
    required this.strokeWidth,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Background ring
    final backgroundPaint = Paint()
      ..color = AppColors.progressBackground
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress ring
    if (progress > 0) {
      final progressPaint = Paint()
        ..shader = LinearGradient(
          colors: colors,
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ).createShader(Rect.fromCircle(center: center, radius: radius))
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      final sweepAngle = 2 * math.pi * progress;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2, // Start from top
        sweepAngle,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(ProgressRingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.colors != colors;
  }
}
