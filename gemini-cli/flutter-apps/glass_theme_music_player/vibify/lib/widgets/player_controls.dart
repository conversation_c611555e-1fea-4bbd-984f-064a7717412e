import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../utils/app_colors.dart';
import '../models/player_state.dart';
import '../services/favorites_service.dart';
import '../services/share_service.dart';

class PlayerControls extends StatefulWidget {
  final MusicPlayerState playerState;
  final VoidCallback? onPlayPause;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final VoidCallback? onShuffle;
  final VoidCallback? onRepeat;
  final bool showSecondaryControls;

  const PlayerControls({
    super.key,
    required this.playerState,
    this.onPlayPause,
    this.onNext,
    this.onPrevious,
    this.onShuffle,
    this.onRepeat,
    this.showSecondaryControls = true,
  });

  @override
  State<PlayerControls> createState() => _PlayerControlsState();
}

class _PlayerControlsState extends State<PlayerControls>
    with TickerProviderStateMixin {
  late AnimationController _playPauseController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _playPauseController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    if (widget.playerState.isPlaying) {
      _playPauseController.forward();
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PlayerControls oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.playerState.isPlaying != oldWidget.playerState.isPlaying) {
      if (widget.playerState.isPlaying) {
        _playPauseController.forward();
        _pulseController.repeat(reverse: true);
      } else {
        _playPauseController.reverse();
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _playPauseController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showSecondaryControls) ...[
            _buildSecondaryControls(),
            const SizedBox(height: 24),
          ],
          _buildMainControls(),
        ],
      ),
    );
  }

  Widget _buildSecondaryControls() {
    final favoritesService = FavoritesService();
    final currentSong = widget.playerState.currentSong;
    final isFavorite = currentSong != null
        ? favoritesService.isFavorite(currentSong)
        : false;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlButton(
          icon: Icons.favorite_border,
          activeIcon: Icons.favorite,
          isActive: isFavorite,
          onTap: currentSong != null
              ? () {
                  favoritesService.toggleFavorite(currentSong);
                }
              : null,
          size: 24,
        ),
        _buildControlButton(
          icon: Icons.shuffle,
          isActive: widget.playerState.isShuffleEnabled,
          onTap: widget.onShuffle,
          size: 24,
        ),
        _buildControlButton(
          icon: _getRepeatIcon(),
          isActive: widget.playerState.repeatMode != RepeatMode.off,
          onTap: widget.onRepeat,
          size: 24,
        ),
        _buildControlButton(
          icon: Icons.share,
          isActive: false,
          onTap: currentSong != null
              ? () {
                  ShareService.shareCurrentlyPlaying(
                    currentSong,
                    widget.playerState.position,
                  );
                }
              : null,
          size: 24,
        ),
      ],
    );
  }

  Widget _buildMainControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlButton(
          icon: Icons.skip_previous,
          onTap: widget.playerState.canGoPrevious ? widget.onPrevious : null,
          size: 32,
          isEnabled: widget.playerState.canGoPrevious,
        ),
        _buildPlayPauseButton(),
        _buildControlButton(
          icon: Icons.skip_next,
          onTap: widget.playerState.canGoNext ? widget.onNext : null,
          size: 32,
          isEnabled: widget.playerState.canGoNext,
        ),
      ],
    );
  }

  Widget _buildPlayPauseButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.playerState.isPlaying ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: widget.onPlayPause,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: AppColors.primaryLinearGradient,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryGradient.first.withOpacity(0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: GlassmorphicContainer(
                width: 80,
                height: 80,
                borderRadius: 40,
                blur: 20,
                alignment: Alignment.center,
                border: 2,
                linearGradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.1),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
                borderGradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                child: AnimatedBuilder(
                  animation: _playPauseController,
                  builder: (context, child) {
                    return Icon(
                      widget.playerState.isPlaying
                          ? Icons.pause
                          : Icons.play_arrow,
                      size: 36,
                      color: AppColors.primaryText,
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    IconData? activeIcon,
    VoidCallback? onTap,
    bool isActive = false,
    bool isEnabled = true,
    double size = 24,
  }) {
    final effectiveIcon = isActive && activeIcon != null ? activeIcon : icon;
    final effectiveColor = isEnabled
        ? (isActive ? AppColors.accentIcon : AppColors.primaryIcon)
        : AppColors.tertiaryText;

    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: size + 24,
        height: size + 24,
        decoration: AppColors.buttonGlassDecoration.copyWith(
          color: isActive
              ? AppColors.buttonPressed
              : AppColors.buttonBackground,
        ),
        child: Center(
          child: Icon(effectiveIcon, size: size, color: effectiveColor),
        ),
      ),
    );
  }

  IconData _getRepeatIcon() {
    switch (widget.playerState.repeatMode) {
      case RepeatMode.off:
        return Icons.repeat;
      case RepeatMode.all:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
    }
  }
}

class MiniPlayerControls extends StatelessWidget {
  final MusicPlayerState playerState;
  final VoidCallback? onPlayPause;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;

  const MiniPlayerControls({
    super.key,
    required this.playerState,
    this.onPlayPause,
    this.onNext,
    this.onPrevious,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildMiniControlButton(
          icon: Icons.skip_previous,
          onTap: playerState.canGoPrevious ? onPrevious : null,
          isEnabled: playerState.canGoPrevious,
        ),
        const SizedBox(width: 8),
        _buildMiniPlayPauseButton(),
        const SizedBox(width: 8),
        _buildMiniControlButton(
          icon: Icons.skip_next,
          onTap: playerState.canGoNext ? onNext : null,
          isEnabled: playerState.canGoNext,
        ),
      ],
    );
  }

  Widget _buildMiniPlayPauseButton() {
    return GestureDetector(
      onTap: onPlayPause,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: AppColors.primaryLinearGradient,
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryGradient.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          playerState.isPlaying ? Icons.pause : Icons.play_arrow,
          size: 20,
          color: AppColors.primaryText,
        ),
      ),
    );
  }

  Widget _buildMiniControlButton({
    required IconData icon,
    VoidCallback? onTap,
    bool isEnabled = true,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: 32,
        height: 32,
        decoration: AppColors.buttonGlassDecoration,
        child: Icon(
          icon,
          size: 16,
          color: isEnabled ? AppColors.primaryIcon : AppColors.tertiaryText,
        ),
      ),
    );
  }
}
