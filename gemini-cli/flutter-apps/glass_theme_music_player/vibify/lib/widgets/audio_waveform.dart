import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class AudioWaveform extends StatefulWidget {
  final bool isPlaying;
  final double progress;
  final double width;
  final double height;
  final int barCount;
  final List<double>? waveformData;

  const AudioWaveform({
    super.key,
    this.isPlaying = false,
    this.progress = 0.0,
    this.width = 300,
    this.height = 80,
    this.barCount = 50,
    this.waveformData,
  });

  @override
  State<AudioWaveform> createState() => _AudioWaveformState();
}

class _AudioWaveformState extends State<AudioWaveform>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _barControllers;
  late List<Animation<double>> _barAnimations;
  List<double> _currentWaveform = [];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _initializeBarAnimations();
    _generateWaveformData();
    
    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  void _initializeBarAnimations() {
    _barControllers = List.generate(
      widget.barCount,
      (index) => AnimationController(
        duration: Duration(
          milliseconds: 300 + (index % 5) * 100, // Varying durations
        ),
        vsync: this,
      ),
    );

    _barAnimations = _barControllers.map((controller) {
      return Tween<double>(
        begin: 0.1,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();
  }

  void _generateWaveformData() {
    if (widget.waveformData != null) {
      _currentWaveform = List.from(widget.waveformData!);
    } else {
      // Generate random waveform data for demo
      final random = math.Random();
      _currentWaveform = List.generate(
        widget.barCount,
        (index) => 0.1 + random.nextDouble() * 0.9,
      );
    }
  }

  void _startAnimations() {
    for (int i = 0; i < _barControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 20), () {
        if (mounted && widget.isPlaying) {
          _barControllers[i].repeat(reverse: true);
        }
      });
    }
  }

  void _stopAnimations() {
    for (final controller in _barControllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  void didUpdateWidget(AudioWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
    
    if (widget.waveformData != oldWidget.waveformData) {
      _generateWaveformData();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _barControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: CustomPaint(
        size: Size(widget.width, widget.height),
        painter: WaveformPainter(
          waveformData: _currentWaveform,
          progress: widget.progress,
          isPlaying: widget.isPlaying,
          barAnimations: _barAnimations,
          colors: AppColors.waveformGradient,
        ),
      ),
    );
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final double progress;
  final bool isPlaying;
  final List<Animation<double>> barAnimations;
  final List<Color> colors;

  WaveformPainter({
    required this.waveformData,
    required this.progress,
    required this.isPlaying,
    required this.barAnimations,
    required this.colors,
  }) : super(repaint: Listenable.merge(barAnimations));

  @override
  void paint(Canvas canvas, Size size) {
    if (waveformData.isEmpty) return;

    final barWidth = size.width / waveformData.length;
    final centerY = size.height / 2;
    final maxBarHeight = size.height * 0.8;

    for (int i = 0; i < waveformData.length; i++) {
      final x = i * barWidth + barWidth / 2;
      final normalizedHeight = waveformData[i];
      
      // Calculate animated height
      double animatedMultiplier = 1.0;
      if (isPlaying && i < barAnimations.length) {
        animatedMultiplier = barAnimations[i].value;
      }
      
      final barHeight = normalizedHeight * maxBarHeight * animatedMultiplier;
      
      // Determine color based on progress and position
      Color barColor;
      final barProgress = i / waveformData.length;
      
      if (barProgress <= progress) {
        // Played portion - use gradient colors
        final colorIndex = (barProgress * (colors.length - 1)).floor();
        final colorProgress = (barProgress * (colors.length - 1)) - colorIndex;
        
        if (colorIndex >= colors.length - 1) {
          barColor = colors.last;
        } else {
          barColor = Color.lerp(
            colors[colorIndex],
            colors[colorIndex + 1],
            colorProgress,
          )!;
        }
        
        // Add glow effect for currently playing area
        if (isPlaying && (barProgress - progress).abs() < 0.05) {
          barColor = barColor.withOpacity(1.0);
        }
      } else {
        // Unplayed portion - use muted colors
        barColor = AppColors.secondaryText.withOpacity(0.3);
      }

      // Draw the bar
      final paint = Paint()
        ..color = barColor
        ..strokeWidth = math.max(1.0, barWidth * 0.6)
        ..strokeCap = StrokeCap.round;

      // Draw top half
      canvas.drawLine(
        Offset(x, centerY),
        Offset(x, centerY - barHeight / 2),
        paint,
      );

      // Draw bottom half (mirrored)
      canvas.drawLine(
        Offset(x, centerY),
        Offset(x, centerY + barHeight / 2),
        paint,
      );

      // Add glow effect for active bars
      if (isPlaying && barProgress <= progress) {
        final glowPaint = Paint()
          ..color = barColor.withOpacity(0.3)
          ..strokeWidth = math.max(2.0, barWidth * 0.8)
          ..strokeCap = StrokeCap.round
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

        canvas.drawLine(
          Offset(x, centerY - barHeight / 2),
          Offset(x, centerY + barHeight / 2),
          glowPaint,
        );
      }
    }

    // Draw progress indicator line
    if (progress > 0) {
      final progressX = progress * size.width;
      final progressPaint = Paint()
        ..color = AppColors.primaryText.withOpacity(0.8)
        ..strokeWidth = 2
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(
        Offset(progressX, 0),
        Offset(progressX, size.height),
        progressPaint,
      );

      // Draw progress indicator circle
      final circlePaint = Paint()
        ..color = AppColors.primaryText
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
        Offset(progressX, centerY),
        4,
        circlePaint,
      );

      // Draw glow around progress indicator
      final glowPaint = Paint()
        ..color = AppColors.primaryGradient.first.withOpacity(0.5)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

      canvas.drawCircle(
        Offset(progressX, centerY),
        8,
        glowPaint,
      );
    }
  }

  @override
  bool shouldRepaint(WaveformPainter oldDelegate) {
    return oldDelegate.waveformData != waveformData ||
        oldDelegate.progress != progress ||
        oldDelegate.isPlaying != isPlaying;
  }
}

// Utility class to generate realistic waveform data
class WaveformGenerator {
  static List<double> generateWaveform({
    int barCount = 50,
    double intensity = 0.8,
    int? seed,
  }) {
    final random = math.Random(seed);
    final waveform = <double>[];
    
    for (int i = 0; i < barCount; i++) {
      // Create more realistic waveform with peaks and valleys
      final baseHeight = 0.1 + random.nextDouble() * intensity;
      
      // Add some smoothing between adjacent bars
      if (i > 0 && waveform.isNotEmpty) {
        final previousHeight = waveform.last;
        final smoothedHeight = (baseHeight + previousHeight) / 2;
        waveform.add(smoothedHeight);
      } else {
        waveform.add(baseHeight);
      }
    }
    
    return waveform;
  }
  
  static List<double> generateBeatsWaveform({
    int barCount = 50,
    double bpm = 120,
    double duration = 180, // seconds
  }) {
    final waveform = <double>[];
    final beatsPerSecond = bpm / 60;
    final secondsPerBar = duration / barCount;
    
    for (int i = 0; i < barCount; i++) {
      final timePosition = i * secondsPerBar;
      final beatPosition = timePosition * beatsPerSecond;
      
      // Create peaks at beat positions
      final beatPhase = (beatPosition % 1.0) * 2 * math.pi;
      final beatIntensity = (math.sin(beatPhase) + 1) / 2;
      
      // Add some randomness
      final random = math.Random(i);
      final randomFactor = 0.3 + random.nextDouble() * 0.7;
      
      final height = 0.1 + beatIntensity * randomFactor * 0.9;
      waveform.add(height);
    }
    
    return waveform;
  }
}
