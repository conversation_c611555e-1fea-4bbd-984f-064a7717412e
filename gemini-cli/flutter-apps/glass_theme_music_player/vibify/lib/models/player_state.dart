import 'song.dart';
import 'playlist.dart';

enum PlaybackState { stopped, playing, paused, buffering, loading }

enum RepeatMode { off, one, all }

class MusicPlayerState {
  final Song? currentSong;
  final Playlist? currentPlaylist;
  final PlaybackState playbackState;
  final Duration position;
  final Duration duration;
  final bool isShuffleEnabled;
  final RepeatMode repeatMode;
  final double volume;
  final int currentIndex;
  final List<Song> queue;
  final bool isLoading;
  final String? error;

  const MusicPlayerState({
    this.currentSong,
    this.currentPlaylist,
    this.playbackState = PlaybackState.stopped,
    this.position = Duration.zero,
    this.duration = Duration.zero,
    this.isShuffleEnabled = false,
    this.repeatMode = RepeatMode.off,
    this.volume = 1.0,
    this.currentIndex = 0,
    this.queue = const [],
    this.isLoading = false,
    this.error,
  });

  bool get isPlaying => playbackState == PlaybackState.playing;
  bool get isPaused => playbackState == PlaybackState.paused;
  bool get isStopped => playbackState == PlaybackState.stopped;
  bool get isBuffering => playbackState == PlaybackState.buffering;
  bool get hasError => error != null;
  bool get hasQueue => queue.isNotEmpty;
  bool get canGoNext => currentIndex < queue.length - 1;
  bool get canGoPrevious => currentIndex > 0;

  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }

  String get formattedPosition {
    final minutes = position.inMinutes;
    final seconds = position.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  MusicPlayerState copyWith({
    Song? currentSong,
    Playlist? currentPlaylist,
    PlaybackState? playbackState,
    Duration? position,
    Duration? duration,
    bool? isShuffleEnabled,
    RepeatMode? repeatMode,
    double? volume,
    int? currentIndex,
    List<Song>? queue,
    bool? isLoading,
    String? error,
    bool clearError = false,
  }) {
    return MusicPlayerState(
      currentSong: currentSong ?? this.currentSong,
      currentPlaylist: currentPlaylist ?? this.currentPlaylist,
      playbackState: playbackState ?? this.playbackState,
      position: position ?? this.position,
      duration: duration ?? this.duration,
      isShuffleEnabled: isShuffleEnabled ?? this.isShuffleEnabled,
      repeatMode: repeatMode ?? this.repeatMode,
      volume: volume ?? this.volume,
      currentIndex: currentIndex ?? this.currentIndex,
      queue: queue ?? this.queue,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MusicPlayerState &&
          runtimeType == other.runtimeType &&
          currentSong == other.currentSong &&
          playbackState == other.playbackState &&
          position == other.position &&
          duration == other.duration &&
          isShuffleEnabled == other.isShuffleEnabled &&
          repeatMode == other.repeatMode &&
          volume == other.volume &&
          currentIndex == other.currentIndex;

  @override
  int get hashCode => Object.hash(
    currentSong,
    playbackState,
    position,
    duration,
    isShuffleEnabled,
    repeatMode,
    volume,
    currentIndex,
  );

  @override
  String toString() {
    return 'PlayerState{currentSong: ${currentSong?.title}, playbackState: $playbackState, position: $formattedPosition, duration: $formattedDuration}';
  }
}
