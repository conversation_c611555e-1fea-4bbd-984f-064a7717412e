import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/download_service.dart';
import '../services/audio_service.dart';
import '../models/song.dart';
import '../utils/app_colors.dart';

class DownloadsScreen extends StatelessWidget {
  const DownloadsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<DownloadService, AudioService>(
      builder: (context, downloadService, audioService, child) {
        final downloadedSongs = downloadService.downloadedSongs;
        final activeDownloads = downloadService.downloads.values
            .where((item) => item.status == DownloadStatus.downloading)
            .toList();

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Text(
                    'Downloads',
                    style: TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (downloadedSongs.isNotEmpty)
                    PopupMenuButton<String>(
                      icon: const Icon(
                        Icons.more_vert,
                        color: AppColors.primaryIcon,
                      ),
                      onSelected: (value) async {
                        if (value == 'clear_all') {
                          _showClearAllDialog(context, downloadService);
                        } else if (value == 'storage_info') {
                          _showStorageInfo(context, downloadService);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'storage_info',
                          child: Text('Storage Info'),
                        ),
                        const PopupMenuItem(
                          value: 'clear_all',
                          child: Text('Clear All Downloads'),
                        ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 8),

              Text(
                '${downloadedSongs.length} songs downloaded',
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 16,
                ),
              ),

              const SizedBox(height: 24),

              // Active Downloads
              if (activeDownloads.isNotEmpty) ...[
                const Text(
                  'Downloading',
                  style: TextStyle(
                    color: AppColors.primaryText,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                ...activeDownloads.map(
                  (download) => _buildDownloadItem(download, downloadService),
                ),
                const SizedBox(height: 24),
              ],

              // Downloaded Songs
              if (downloadedSongs.isNotEmpty) ...[
                const Text(
                  'Downloaded Songs',
                  style: TextStyle(
                    color: AppColors.primaryText,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: downloadedSongs.length,
                    itemBuilder: (context, index) {
                      final song = downloadedSongs[index];
                      final isCurrentSong =
                          audioService.playerState.currentSong?.id == song.id;

                      return _buildDownloadedSongItem(
                        context,
                        song,
                        isCurrentSong,
                        audioService,
                        downloadService,
                      );
                    },
                  ),
                ),
              ] else if (activeDownloads.isEmpty) ...[
                Expanded(child: _buildEmptyState()),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDownloadItem(
    DownloadItem download,
    DownloadService downloadService,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: AppColors.cardGlassDecoration,
      child: Row(
        children: [
          // Album Art
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: download.song.albumArt.isNotEmpty
                  ? Image.network(
                      download.song.albumArt,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            gradient: AppColors.secondaryLinearGradient,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.music_note,
                            color: AppColors.primaryText,
                            size: 24,
                          ),
                        );
                      },
                    )
                  : Container(
                      decoration: BoxDecoration(
                        gradient: AppColors.secondaryLinearGradient,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.music_note,
                        color: AppColors.primaryText,
                        size: 24,
                      ),
                    ),
            ),
          ),

          const SizedBox(width: 12),

          // Song Info and Progress
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  download.song.title,
                  style: const TextStyle(
                    color: AppColors.primaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  download.song.artist,
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: download.progress,
                  backgroundColor: AppColors.progressBackground,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.progressForeground,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(download.progress * 100).toInt()}%',
                  style: const TextStyle(
                    color: AppColors.tertiaryText,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),

          // Cancel Button
          IconButton(
            onPressed: () {
              downloadService.cancelDownload(download.song.id);
            },
            icon: const Icon(Icons.close, color: AppColors.error),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadedSongItem(
    BuildContext context,
    Song song,
    bool isCurrentSong,
    AudioService audioService,
    DownloadService downloadService,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isCurrentSong
            ? AppColors.accentIcon.withOpacity(0.1)
            : Colors.transparent,
        border: Border.all(
          color: isCurrentSong
              ? AppColors.accentIcon.withOpacity(0.3)
              : AppColors.glassBorder,
          width: 0.5,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: song.albumArt.isNotEmpty
                ? Image.network(
                    song.albumArt,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          gradient: AppColors.secondaryLinearGradient,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.music_note,
                          color: AppColors.primaryText,
                          size: 24,
                        ),
                      );
                    },
                  )
                : Container(
                    decoration: BoxDecoration(
                      gradient: AppColors.secondaryLinearGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.music_note,
                      color: AppColors.primaryText,
                      size: 24,
                    ),
                  ),
          ),
        ),
        title: Text(
          song.title,
          style: TextStyle(
            color: isCurrentSong ? AppColors.accentText : AppColors.primaryText,
            fontSize: 14,
            fontWeight: isCurrentSong ? FontWeight.w600 : FontWeight.w500,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Row(
          children: [
            Expanded(
              child: Text(
                song.artist,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 12,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.download_done, color: AppColors.success, size: 16),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              song.formattedDuration,
              style: const TextStyle(
                color: AppColors.tertiaryText,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const Icon(
                Icons.more_vert,
                color: AppColors.secondaryIcon,
                size: 20,
              ),
              onSelected: (value) {
                if (value == 'delete') {
                  _showDeleteDialog(context, song, downloadService);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete Download'),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          if (isCurrentSong) {
            if (audioService.playerState.isPlaying) {
              audioService.pause();
            } else {
              audioService.play();
            }
          } else {
            audioService.loadSong(song);
            audioService.play();
          }
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.download,
            size: 80,
            color: AppColors.tertiaryText.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'No downloads yet',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Downloaded songs will appear here',
            style: TextStyle(color: AppColors.tertiaryText, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    Song song,
    DownloadService downloadService,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Download'),
        content: Text('Are you sure you want to delete "${song.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              downloadService.removeDownload(song.id);
              Navigator.pop(context);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(
    BuildContext context,
    DownloadService downloadService,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Downloads'),
        content: const Text(
          'Are you sure you want to delete all downloaded songs?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              downloadService.clearAllDownloads();
              Navigator.pop(context);
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo(
    BuildContext context,
    DownloadService downloadService,
  ) async {
    final size = await downloadService.getDownloadedSize();
    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Storage Info'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Downloaded songs: ${downloadService.downloadedSongs.length}',
              ),
              const SizedBox(height: 8),
              Text('Total size: ${downloadService.formatFileSize(size)}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
