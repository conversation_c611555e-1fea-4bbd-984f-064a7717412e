import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import '../utils/app_colors.dart';
import '../widgets/glass_bottom_nav.dart';
import 'home_screen.dart';
import 'library_screen.dart';
import 'search_screen.dart';
import 'favorites_screen.dart';
import 'downloads_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(icon: Icons.home, activeIcon: Icons.home, label: 'Home'),
    NavigationItem(
      icon: Icons.search,
      activeIcon: Icons.search,
      label: 'Search',
    ),
    NavigationItem(
      icon: Icons.library_music,
      activeIcon: Icons.library_music,
      label: 'Library',
    ),
    NavigationItem(
      icon: Icons.download_outlined,
      activeIcon: Icons.download,
      label: 'Downloads',
    ),
    NavigationItem(
      icon: Icons.favorite_border,
      activeIcon: Icons.favorite,
      label: 'Favorites',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onNavigationTap(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primaryBackground,
              AppColors.secondaryBackground,
              AppColors.cardBackground,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              _buildAppBar(),

              // Page Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  children: const [
                    HomeScreen(),
                    SearchScreen(),
                    LibraryScreen(),
                    DownloadsScreen(),
                    FavoritesScreen(),
                  ],
                ),
              ),

              // Bottom Navigation
              GlassBottomNav(
                currentIndex: _currentIndex,
                items: _navigationItems,
                onTap: _onNavigationTap,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // Logo/Title
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColors.primaryLinearGradient,
                ),
                child: const Icon(
                  Icons.music_note,
                  color: AppColors.primaryText,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Vibify',
                style: TextStyle(
                  color: AppColors.primaryText,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const Spacer(),

          // Action buttons
          Row(
            children: [
              _buildActionButton(
                icon: Icons.notifications_none,
                onTap: () {
                  // TODO: Implement notifications
                },
              ),
              const SizedBox(width: 8),
              _buildActionButton(
                icon: Icons.settings,
                onTap: () {
                  // TODO: Implement settings
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: AppColors.buttonGlassDecoration,
        child: Icon(icon, color: AppColors.primaryIcon, size: 20),
      ),
    );
  }
}

class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
