import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/favorites_service.dart';
import '../services/audio_service.dart';
import '../utils/app_colors.dart';

class FavoritesScreen extends StatelessWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<FavoritesService, AudioService>(
      builder: (context, favoritesService, audioService, child) {
        final favoriteSongs = favoritesService.favoriteSongs;
        
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Text(
                    'Favorites',
                    style: TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (favoriteSongs.isNotEmpty)
                    IconButton(
                      onPressed: () {
                        // TODO: Shuffle play all favorites
                      },
                      icon: const Icon(
                        Icons.shuffle,
                        color: AppColors.accentIcon,
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              Text(
                '${favoriteSongs.length} songs',
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 16,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Favorites List
              Expanded(
                child: favoriteSongs.isEmpty
                    ? _buildEmptyState()
                    : _buildFavoritesList(favoriteSongs, audioService),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: AppColors.tertiaryText.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'No favorites yet',
            style: TextStyle(
              color: AppColors.secondaryText,
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Songs you like will appear here',
            style: TextStyle(
              color: AppColors.tertiaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList(List favoriteSongs, AudioService audioService) {
    return ListView.builder(
      itemCount: favoriteSongs.length,
      itemBuilder: (context, index) {
        final song = favoriteSongs[index];
        final isCurrentSong = audioService.playerState.currentSong?.id == song.id;
        
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isCurrentSong
                ? AppColors.accentIcon.withOpacity(0.1)
                : Colors.transparent,
            border: isCurrentSong
                ? Border.all(
                    color: AppColors.accentIcon.withOpacity(0.3),
                    width: 1,
                  )
                : null,
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(12),
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                gradient: AppColors.secondaryLinearGradient,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: song.albumArt.isNotEmpty
                    ? Image.network(
                        song.albumArt,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.music_note,
                            color: AppColors.primaryText,
                            size: 24,
                          );
                        },
                      )
                    : const Icon(
                        Icons.music_note,
                        color: AppColors.primaryText,
                        size: 24,
                      ),
              ),
            ),
            title: Text(
              song.title,
              style: TextStyle(
                color: isCurrentSong
                    ? AppColors.accentText
                    : AppColors.primaryText,
                fontSize: 14,
                fontWeight: isCurrentSong
                    ? FontWeight.w600
                    : FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              song.artist,
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  song.formattedDuration,
                  style: const TextStyle(
                    color: AppColors.tertiaryText,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {
                    if (isCurrentSong) {
                      if (audioService.playerState.isPlaying) {
                        audioService.pause();
                      } else {
                        audioService.play();
                      }
                    } else {
                      audioService.loadSong(song);
                      audioService.play();
                    }
                  },
                  icon: Icon(
                    isCurrentSong && audioService.playerState.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: AppColors.accentIcon,
                  ),
                ),
              ],
            ),
            onTap: () {
              if (isCurrentSong) {
                if (audioService.playerState.isPlaying) {
                  audioService.pause();
                } else {
                  audioService.play();
                }
              } else {
                audioService.loadSong(song);
                audioService.play();
              }
            },
          ),
        );
      },
    );
  }
}
