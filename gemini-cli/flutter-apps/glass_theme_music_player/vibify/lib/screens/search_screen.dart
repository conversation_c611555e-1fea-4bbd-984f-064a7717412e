import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../utils/app_colors.dart';
import '../services/youtube_service.dart';
import '../services/audio_service.dart';
import '../services/download_service.dart';
import '../models/song.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final YouTubeService _youtubeService = YouTubeService();
  String _searchQuery = '';
  List<Song> _searchResults = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results = await _youtubeService.searchSongs(query, maxResults: 20);
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Search failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Bar
          _buildSearchBar(),

          const SizedBox(height: 24),

          // Search Results or Browse Categories
          Expanded(
            child: _searchQuery.isEmpty
                ? _buildBrowseCategories()
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: AppColors.glassDecoration,
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: AppColors.primaryText),
        decoration: const InputDecoration(
          hintText: 'Search songs, artists, albums...',
          hintStyle: TextStyle(color: AppColors.tertiaryText),
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search, color: AppColors.secondaryIcon),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          // Debounce search to avoid too many API calls
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_searchController.text == value) {
              _performSearch(value);
            }
          });
        },
        onSubmitted: (value) {
          _performSearch(value);
        },
      ),
    );
  }

  Widget _buildBrowseCategories() {
    final categories = [
      {'name': 'Pop', 'color': Colors.pink},
      {'name': 'Rock', 'color': Colors.red},
      {'name': 'Hip Hop', 'color': Colors.orange},
      {'name': 'Electronic', 'color': Colors.blue},
      {'name': 'Jazz', 'color': Colors.purple},
      {'name': 'Classical', 'color': Colors.green},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Browse Categories',
          style: TextStyle(
            color: AppColors.primaryText,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    colors: [
                      (category['color'] as Color).withOpacity(0.8),
                      (category['color'] as Color).withOpacity(0.4),
                    ],
                  ),
                ),
                child: Center(
                  child: Text(
                    category['name'] as String,
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    return Consumer2<AudioService, DownloadService>(
      builder: (context, audioService, downloadService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Search results for "$_searchQuery"',
                    style: const TextStyle(
                      color: AppColors.primaryText,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (_isSearching)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.accentIcon,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _isSearching
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.accentIcon,
                      ),
                    )
                  : _searchResults.isEmpty
                  ? const Center(
                      child: Text(
                        'No results found',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final song = _searchResults[index];
                        final isCurrentSong =
                            audioService.playerState.currentSong?.id == song.id;

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: isCurrentSong
                                ? AppColors.accentIcon.withOpacity(0.1)
                                : Colors.transparent,
                            border: Border.all(
                              color: AppColors.glassBorder,
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            children: [
                              // Album Art
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: song.albumArt.isNotEmpty
                                      ? Image.network(
                                          song.albumArt,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                                return Container(
                                                  decoration: BoxDecoration(
                                                    gradient: AppColors
                                                        .secondaryLinearGradient,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  child: const Icon(
                                                    Icons.music_note,
                                                    color:
                                                        AppColors.primaryText,
                                                    size: 24,
                                                  ),
                                                );
                                              },
                                        )
                                      : Container(
                                          decoration: BoxDecoration(
                                            gradient: AppColors
                                                .secondaryLinearGradient,
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: const Icon(
                                            Icons.music_note,
                                            color: AppColors.primaryText,
                                            size: 24,
                                          ),
                                        ),
                                ),
                              ),
                              const SizedBox(width: 12),

                              // Song Info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      song.title,
                                      style: TextStyle(
                                        color: isCurrentSong
                                            ? AppColors.accentText
                                            : AppColors.primaryText,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      song.artist,
                                      style: const TextStyle(
                                        color: AppColors.secondaryText,
                                        fontSize: 12,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (song.duration != Duration.zero)
                                      Text(
                                        song.formattedDuration,
                                        style: const TextStyle(
                                          color: AppColors.tertiaryText,
                                          fontSize: 10,
                                        ),
                                      ),
                                  ],
                                ),
                              ),

                              // Download Button
                              if (downloadService.isDownloaded(song.id))
                                const Icon(
                                  Icons.download_done,
                                  color: AppColors.success,
                                  size: 20,
                                )
                              else if (downloadService.isDownloading(song.id))
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    value: downloadService.getDownloadProgress(
                                      song.id,
                                    ),
                                    strokeWidth: 2,
                                    color: AppColors.accentIcon,
                                  ),
                                )
                              else
                                IconButton(
                                  onPressed: () {
                                    try {
                                      downloadService.downloadSong(song);
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Downloading "${song.title}"',
                                          ),
                                          duration: const Duration(seconds: 2),
                                        ),
                                      );
                                    } catch (e) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text('Download failed: $e'),
                                          backgroundColor: AppColors.error,
                                        ),
                                      );
                                    }
                                  },
                                  icon: const Icon(
                                    Icons.download,
                                    color: AppColors.secondaryIcon,
                                    size: 20,
                                  ),
                                ),

                              // Play Button
                              IconButton(
                                onPressed: () {
                                  if (isCurrentSong) {
                                    if (audioService.playerState.isPlaying) {
                                      audioService.pause();
                                    } else {
                                      audioService.play();
                                    }
                                  } else {
                                    // Use downloaded version if available
                                    final downloadedSong = downloadService
                                        .getDownloadedSong(song.id);
                                    audioService.loadSong(
                                      downloadedSong ?? song,
                                    );
                                    audioService.play();
                                  }
                                },
                                icon: Icon(
                                  isCurrentSong &&
                                          audioService.playerState.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: AppColors.accentIcon,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
          ],
        );
      },
    );
  }
}
