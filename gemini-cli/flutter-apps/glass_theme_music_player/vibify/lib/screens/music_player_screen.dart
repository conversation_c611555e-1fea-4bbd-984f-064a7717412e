import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/audio_service.dart';
import '../widgets/circular_album_art.dart';
import '../widgets/audio_waveform.dart';
import '../widgets/player_controls.dart';
import '../widgets/playlist_widget.dart';
import '../utils/app_colors.dart';
import '../models/song.dart';

class MusicPlayerScreen extends StatefulWidget {
  const MusicPlayerScreen({super.key});

  @override
  State<MusicPlayerScreen> createState() => _MusicPlayerScreenState();
}

class _MusicPlayerScreenState extends State<MusicPlayerScreen> {
  late AudioService _audioService;

  @override
  void initState() {
    super.initState();
    _audioService = AudioService();
    _audioService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _audioService,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primaryBackground,
                AppColors.secondaryBackground,
                AppColors.cardBackground,
              ],
            ),
          ),
          child: SafeArea(
            child: Consumer<AudioService>(
              builder: (context, audioService, child) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    final isWideScreen = constraints.maxWidth > 800;
                    
                    if (isWideScreen) {
                      return _buildWideLayout(audioService);
                    } else {
                      return _buildNarrowLayout(audioService);
                    }
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWideLayout(AudioService audioService) {
    return Row(
      children: [
        // Left side - Player
        Expanded(
          flex: 2,
          child: _buildPlayerSection(audioService),
        ),
        
        // Right side - Playlist
        SizedBox(
          width: 400,
          child: PlaylistWidget(
            playlist: audioService.playerState.currentPlaylist,
            playerState: audioService.playerState,
            onSongTap: (song, index) => _onSongTap(audioService, song, index),
            onShuffleAll: () => _onShuffleAll(audioService),
          ),
        ),
      ],
    );
  }

  Widget _buildNarrowLayout(AudioService audioService) {
    return Column(
      children: [
        // Top section - Player
        Expanded(
          child: _buildPlayerSection(audioService),
        ),
        
        // Bottom section - Mini playlist or controls
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          child: PlaylistWidget(
            playlist: audioService.playerState.currentPlaylist,
            playerState: audioService.playerState,
            onSongTap: (song, index) => _onSongTap(audioService, song, index),
            onShuffleAll: () => _onShuffleAll(audioService),
            width: double.infinity,
          ),
        ),
      ],
    );
  }

  Widget _buildPlayerSection(AudioService audioService) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
          // Header with song info
          _buildSongHeader(audioService.playerState.currentSong),
          
          const SizedBox(height: 40),
          
          // Album art
          CircularAlbumArt(
            song: audioService.playerState.currentSong,
            progress: audioService.playerState.progress,
            isPlaying: audioService.playerState.isPlaying,
            size: 280,
            onTap: () => _onAlbumArtTap(audioService),
          ),
          
          const SizedBox(height: 32),
          
          // Song title and artist
          _buildSongInfo(audioService.playerState.currentSong),
          
          const SizedBox(height: 24),
          
          // Waveform
          AudioWaveform(
            isPlaying: audioService.playerState.isPlaying,
            progress: audioService.playerState.progress,
            width: 320,
            height: 80,
          ),
          
          const SizedBox(height: 24),
          
          // Progress bar
          _buildProgressBar(audioService),
          
          const SizedBox(height: 32),
          
          // Player controls
          PlayerControls(
            playerState: audioService.playerState,
            onPlayPause: () => _onPlayPause(audioService),
            onNext: () => _onNext(audioService),
            onPrevious: () => _onPrevious(audioService),
            onShuffle: () => _onShuffle(audioService),
            onRepeat: () => _onRepeat(audioService),
          ),
        ],
      ),
    );
  }

  Widget _buildSongHeader(Song? song) {
    if (song == null) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              song.title,
              style: const TextStyle(
                color: AppColors.primaryText,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              song.artist,
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontSize: 16,
              ),
            ),
          ],
        ),
        Row(
          children: [
            IconButton(
              onPressed: () {
                // TODO: Implement favorite toggle
              },
              icon: const Icon(
                Icons.favorite_border,
                color: AppColors.primaryIcon,
              ),
            ),
            IconButton(
              onPressed: () {
                // TODO: Implement share functionality
              },
              icon: const Icon(
                Icons.share,
                color: AppColors.primaryIcon,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSongInfo(Song? song) {
    if (song == null) {
      return Column(
        children: [
          Text(
            'No song selected',
            style: const TextStyle(
              color: AppColors.secondaryText,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Choose a song to play',
            style: const TextStyle(
              color: AppColors.tertiaryText,
              fontSize: 14,
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        Text(
          song.title,
          style: const TextStyle(
            color: AppColors.primaryText,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          song.artist,
          style: const TextStyle(
            color: AppColors.secondaryText,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildProgressBar(AudioService audioService) {
    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            activeTrackColor: AppColors.progressForeground,
            inactiveTrackColor: AppColors.progressBackground,
            thumbColor: AppColors.sliderThumb,
            overlayColor: AppColors.progressForeground.withOpacity(0.2),
          ),
          child: Slider(
            value: audioService.playerState.progress.clamp(0.0, 1.0),
            onChanged: (value) {
              final position = audioService.playerState.duration * value;
              audioService.seek(position);
            },
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              audioService.playerState.formattedPosition,
              style: const TextStyle(
                color: AppColors.tertiaryText,
                fontSize: 12,
              ),
            ),
            Text(
              audioService.playerState.formattedDuration,
              style: const TextStyle(
                color: AppColors.tertiaryText,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Event handlers
  void _onPlayPause(AudioService audioService) {
    if (audioService.playerState.isPlaying) {
      audioService.pause();
    } else {
      audioService.play();
    }
  }

  void _onNext(AudioService audioService) {
    audioService.next();
  }

  void _onPrevious(AudioService audioService) {
    audioService.previous();
  }

  void _onShuffle(AudioService audioService) {
    audioService.toggleShuffle();
  }

  void _onRepeat(AudioService audioService) {
    audioService.toggleRepeat();
  }

  void _onAlbumArtTap(AudioService audioService) {
    _onPlayPause(audioService);
  }

  void _onSongTap(AudioService audioService, Song song, int index) {
    if (audioService.playerState.currentPlaylist != null) {
      audioService.loadPlaylist(
        audioService.playerState.currentPlaylist!,
        startIndex: index,
      );
      audioService.play();
    }
  }

  void _onShuffleAll(AudioService audioService) {
    if (audioService.playerState.currentPlaylist != null) {
      audioService.toggleShuffle();
      audioService.play();
    }
  }
}
