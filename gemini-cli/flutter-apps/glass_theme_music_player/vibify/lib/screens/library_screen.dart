import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/audio_service.dart';
import '../utils/app_colors.dart';
import '../utils/sample_data.dart';

class LibraryScreen extends StatelessWidget {
  const LibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioService>(
      builder: (context, audioService, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              const Text(
                'Your Library',
                style: TextStyle(
                  color: AppColors.primaryText,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Quick Stats
              _buildQuickStats(),
              
              const SizedBox(height: 24),
              
              // Library Sections
              _buildLibrarySection(
                title: 'Playlists',
                items: SampleData.samplePlaylists,
                onTap: (playlist) {
                  audioService.loadPlaylist(playlist);
                },
              ),
              
              const SizedBox(height: 24),
              
              _buildLibrarySection(
                title: 'Recent Songs',
                items: SampleData.sampleSongs.take(5).toList(),
                onTap: (song) {
                  audioService.loadSong(song);
                  audioService.play();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.queue_music,
            title: '${SampleData.sampleSongs.length}',
            subtitle: 'Songs',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.playlist_play,
            title: '${SampleData.samplePlaylists.length}',
            subtitle: 'Playlists',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.access_time,
            title: '12h',
            subtitle: 'Total Time',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppColors.cardGlassDecoration,
      child: Column(
        children: [
          Icon(
            icon,
            color: AppColors.accentIcon,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              color: AppColors.primaryText,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            subtitle,
            style: const TextStyle(
              color: AppColors.secondaryText,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLibrarySection<T>({
    required String title,
    required List<T> items,
    required Function(T) onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                color: AppColors.primaryText,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Show all
              },
              child: const Text(
                'See All',
                style: TextStyle(
                  color: AppColors.accentText,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          itemBuilder: (context, index) {
            final item = items[index];
            return GestureDetector(
              onTap: () => onTap(item),
              child: Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: AppColors.cardGlassDecoration,
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        gradient: AppColors.secondaryLinearGradient,
                      ),
                      child: const Icon(
                        Icons.music_note,
                        color: AppColors.primaryText,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getItemTitle(item),
                            style: const TextStyle(
                              color: AppColors.primaryText,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _getItemSubtitle(item),
                            style: const TextStyle(
                              color: AppColors.secondaryText,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => onTap(item),
                      icon: const Icon(
                        Icons.play_arrow,
                        color: AppColors.accentIcon,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  String _getItemTitle(dynamic item) {
    if (item.runtimeType.toString().contains('Song')) {
      return item.title;
    } else if (item.runtimeType.toString().contains('Playlist')) {
      return item.name;
    }
    return 'Unknown';
  }

  String _getItemSubtitle(dynamic item) {
    if (item.runtimeType.toString().contains('Song')) {
      return item.artist;
    } else if (item.runtimeType.toString().contains('Playlist')) {
      return '${item.songCount} songs';
    }
    return 'Unknown';
  }
}
